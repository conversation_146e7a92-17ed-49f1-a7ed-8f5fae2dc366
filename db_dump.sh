#!/bin/sh
# Database Import Script
# This script imports a SQL dump file into a fresh TimescaleDB instance

# Get parameters
database="$1"
sql_dump="$2"

# How to use:
# run the command below in your terminal
# ./db_dump.sh <database_name> <sql_dump_file>

# Check if database and sql_dump variables are provided
if [ -z "$database" ] || [ -z "$sql_dump" ]; then
    echo "Usage: $0 <database_name> <sql_dump_file>"
    echo ""
    echo "Example:"
    echo "  ./db_dump.sh prs backup.sql"
    echo "  ./db_dump.sh richard_pre_prod production_backup.sql"
    exit 1
fi

# Validate if sql_dump file exists
if [ ! -f "$sql_dump" ]; then
    echo "Error: SQL dump file '$sql_dump' not found!"
    echo "Please ensure the backup file exists in the current directory"
    exit 1
fi

echo "=== Database Import Process Started ==="
echo "Database: $database"
echo "SQL Dump: $sql_dump"
echo ""

# Check if the 'postgres' service is running and then shut it down
if docker compose ps | grep -q 'postgres'; then
    echo "Stopping and removing the 'postgres' service..."
    docker compose down postgres
else
    echo "The 'postgres' service is not running. Proceeding to the next step."
fi

# Remove database directories (updated paths to match docker-compose.yml)
echo 'Removing old data'
if [ -d "./cityland_db" ]; then
    echo "Removing ./cityland_db directory..."
    rm -rf ./cityland_db
fi

# Create the postgres container with TimescaleDB
echo 'Creating TimescaleDB postgres container...'
docker-compose up -d postgres

# Wait for the postgres container to be fully up and ready
echo "Waiting for TimescaleDB postgres container to be ready..."
until docker exec postgres pg_isready -U admin; do
  echo "Waiting for database to be ready..."
  sleep 2
done
echo "TimescaleDB postgres container is ready."

# Additional wait to ensure TimescaleDB is fully initialized
echo "Waiting for TimescaleDB to fully initialize..."
sleep 10

# Check if the database exists and create it if it doesn't
if ! docker exec postgres psql -U admin -lqt | cut -d \| -f 1 | grep -qw "$database"; then
    echo "Database '$database' does not exist. Creating it now."
    docker exec postgres psql -U admin -d postgres -c "CREATE DATABASE $database;"
else
    echo "Database '$database' already exists."
fi

# Enable TimescaleDB extension
echo 'Enabling TimescaleDB extension...'
docker exec postgres psql -U admin -d "$database" -c "CREATE EXTENSION IF NOT EXISTS timescaledb;"

# Create prs_user role if it doesn't exist
echo 'Creating prs_user role if it does not exist...'
docker exec postgres psql -U admin -d postgres -c "DO \$\$ BEGIN IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'prs_user') THEN CREATE ROLE prs_user; END IF; END \$\$;"

# Additional wait before importing
echo 'Waiting for database to be fully ready for import...'
sleep 5

# Restore the SQL dump
echo 'Importing dump...'
docker exec -i postgres psql -U admin -d "$database" < "$sql_dump"

# Verify TimescaleDB hypertables after import
echo 'Verifying TimescaleDB setup...'
docker exec postgres psql -U admin -d "$database" -c "SELECT hypertable_name, num_chunks FROM timescaledb_information.hypertables LIMIT 5;"

echo 'Database import completed successfully!'
