# prs-infra
## Getting started

## Steps:
1. ecr login: aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 545009862155.dkr.ecr.ap-southeast-1.amazonaws.com
2. docker-compose pull
3. docker compose up

# Update the package index
sudo yum update -y

# Install Git
sudo yum install -y git

<!-- ## Install git
sudo dnf install git-all -->

## Docker setup in ec2
sudo dnf update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user

## Instal docker compose
sudo curl -L https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m) -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
docker-compose version

# Verify installations
git --version
docker --version
docker-compose --version

## Mounting of partition: EC2
1. Log into Your EC2 Instance:
2. Format the EBS Volume: Once logged in, format the newly attached volume (replace /dev/sdf with the actual device name if different):
```bash
sudo mkfs -t ext4 /dev/sdf # why we need to format the EBS volume?
```
3. Mount the EBS Volume:
```bash
# Create a directory to mount the volume:
sudo mkdir /mnt/my_partition
```
```bash
# Mount the volume:
sudo mount /dev/sdf /mnt/my_partition
```
4. Update docker-compose.yml: In your docker-compose.yml, adjust your volume mounts to point to this mounted directory. For example.
```yml
services:
  nginx:
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - /mnt/my_partition/nginx_logs:/var/log/nginx

  backend:
    volumes:
      - /mnt/my_partition/backend_logs:/var/log/backend
```
5. Persist the Mount Across Reboots:
```bash
# To ensure the EBS volume remains mounted after a reboot, you can add an entry to the /etc/fstab file. Open the file:
sudo nano /etc/fstab
```
```bash
# Add the following line (make sure to replace /dev/sdf with the appropriate device name):
/dev/sdf   /mnt/my_partition   ext4   defaults,nofail   0   2
```
5. Run docker compose.
```bash
docker-compose up -d
```

## Steps to Set Up RAID 1 with Two EBS Volumes on EC2
1. Launch an EC2 Instance:
2. Create Two EBS Volumes:
- Navigate to the Volumes section under Elastic Block Store in the EC2 dashboard.
- Click Create Volume twice, specifying the same size and type for both volumes (e.g., General Purpose SSD (gp2) and 10 GiB).
- Ensure both volumes are in the same availability zone as your EC2 instance.
3. Attach Both EBS Volumes to the EC2 Instance:
- Select the first volume and click Actions > Attach Volume. Choose your EC2 instance and attach it as /dev/sdf.
- Repeat this for the second volume, but attach it as /dev/sdg.
4. Log into Your EC2 Instance
5. Install mdadm for RAID Configuration:
```bash
# Update your package manager and install mdadm:
sudo yum update -y      # For Amazon Linux
sudo yum install mdadm -y
```
```bash
# For Ubuntu, use:
sudo apt-get update -y
sudo apt-get install mdadm -y
```
6. Create the RAID 1 Array: First, you need to format both EBS volumes. Replace /dev/sdf and /dev/sdg with the actual device names if different.
```bash
sudo mkfs.ext4 /dev/sdf
sudo mkfs.ext4 /dev/sdg
```
```bash
# Now, create the RAID 1 array:
sudo mdadm --create --verbose /dev/md0 --level=1 --raid-devices=2 /dev/sdf /dev/sdg
```
7. Check the RAID Status:
```bash
# You can check the status of the RAID array with the following command:
cat /proc/mdstat
```
8. Format the RAID Device:
```bash
# Format the RAID array:
sudo mkfs.ext4 /dev/md0
```
9. Create a Mount Point for the RAID Device:
```bash
# Create a directory to mount the RAID device:
sudo mkdir /mnt/my_raid_partition
```
10. Mount the RAID Device:
```bash
# Mount the RAID device:
sudo mount /dev/md0 /mnt/my_raid_partition
```
11. Update docker-compose.yml:
```yml
services:
  nginx:
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - /mnt/my_raid_partition/nginx_logs:/var/log/nginx

  backend:
    volumes:
      - /mnt/my_raid_partition/backend_logs:/var/log/backend
```
12. Persist the Mount Across Reboots:
```bash
# To ensure the RAID device remains mounted after a reboot, add an entry to the /etc/fstab file:
sudo nano /etc/fstab
```
```bash
# Add the following line:
/dev/md0   /mnt/my_raid_partition   ext4   defaults,nofail   0   2
```
13. Run docker compose
```bash
docker-compose up -d
```

## ClamAV sample use
```javascript
const { exec } = require('child_process');
const path = '/usr/app/upload/your-file.jpg';  // Path to uploaded file

exec(`clamscan ${path}`, (error, stdout, stderr) => {
  if (error) {
    console.error(`Error scanning file: ${stderr}`);
    return;
  }

  if (stdout.includes('Infected')) {
    console.log('File is infected:', stdout);
    // Handle infected file (e.g., delete it or reject upload)
  } else {
    console.log('File is clean:', stdout);
  }
});
```

## References:
[Step by Step process of how to add and mount EBS Volume on Ubuntu EC2 Linux Instance](https://medium.com/@mudasirhaji/step-by-step-process-of-how-to-add-and-mount-ebs-volume-on-ubuntu-ec2-linux-instance-a4be8870a4dd)
[How To Create RAID Arrays with mdadm on Ubuntu](https://www.digitalocean.com/community/tutorials/how-to-create-raid-arrays-with-mdadm-on-ubuntu)