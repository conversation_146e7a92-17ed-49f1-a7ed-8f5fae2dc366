PORT=4000
HOST=0.0.0.0
NODE_ENV=development
JWT_SECRET=<JWT_ACCESS_TOKEN_SECRET>
OTP_KEY=<OTPC_SECRET_FOR_ENCRYPTION> # Minimum 32 characters - crypto encrypt

GMAIL_USER=<GOOGE_EMAIL>
GMAIL_PASS=<GOOGLE_APP_PASSWORD>

POSTGRES_HOST=postgres
POSTGRES_DB=prs
POSTGRES_PORT=5432
POSTGRES_USER=admin
POSTGRES_PASSWORD=admin
DIALECT=postgres

POOL_MIN=0
POOL_MAX=5
POOL_ACQUIRE=30000
POOL_IDLE=10000

ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin

BACKEND_IMAGE=<BE_IMAGE_REPO>
FRONTEND_IMAGE=<FE_IMAGE_REPO>
WORKER_IMAGE==<WORKER_IMAGE_REPO>

V_NGINX_LOGS=../logs/nginx_logs
V_BE_LOGS=../logs/be_logs
V_PG_DATA=../db/postgresql_data
V_PG_LOGS=../db/postgres_logs
V_UPLOADS=../upload
V_UPLOAD_LOGS=../upload/logs

# worker
V_WORKER_LOGS=../logs/worker_logs

# Super Admin - Root User
ADMIN_USER_NAME=superadmin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=superadmin

# API Integration
CITYLAND_API_URL=https://cmd-test.free.beeceptor.com