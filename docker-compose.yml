services:
  nginx:
    image: nginx:latest
    container_name: nginx
    ports:
      - "80:80"
      # - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ${V_NGINX_LOGS}:/var/log/nginx
      # - ./letsencrypt:/etc/letsencrypt
    depends_on:
      - frontend
      - backend
    networks:
      - frontend_network
      - backend_network
    env_file:
      - .env
    restart: always

  frontend:
    # build:
    #   context: ../frontend
    #   dockerfile: Dockerfile
    image: ${FRONTEND_IMAGE}
    container_name: frontend
    ports:
      - "3000:3000"
    networks:
      - frontend_network
    restart: always

  backend:
    # build:
    #   context: ../prs-backend
    #   dockerfile: Dockerfile
    container_name: backend
    image: ${BACKEND_IMAGE}
    volumes:
      - ${V_BE_LOGS}:/usr/app/logs
      - ${V_UPLOADS}:/usr/app/upload # <-- Mount the upload directory
    ports:
      - "4000:4000"
    depends_on:
      - postgres
      - redis
    networks:
      - backend_network
    env_file:
      - .env
    restart: always

  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: postgres
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
      POSTGRES_DB: richard_pre_prod
    volumes:
      - ${V_PG_DATA}:/var/lib/postgresql/data
      - ${V_PG_LOGS}:/var/log/postgresql
    ports:
      - "5432:5432"
    networks:
      - backend_network
    env_file:
      - .env
    restart: always
    command: >
      postgres
      -c shared_preload_libraries=timescaledb

  clamav:
    image: clamav/clamav
    container_name: clamav
    ports:
      - "3310:3310"
    volumes:
      - ${V_UPLOADS}:/usr/local/share/clamav/upload # <-- Ensure ClamAV can access the upload directory
      - ${V_UPLOAD_LOGS}:/var/log/clamav
    networks:
      - backend_network
    restart: always

  redis:
    image: redis:latest
    container_name: redis
    # No need to expose ports to host unless you need to access Redis from outside Docker
    # For internal use by backend and ETL worker, just ensure they're on the same network
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - backend_network
    restart: always

  backend-worker:
    # build: # for local testing
    #   context: ../prs-backend
    #   dockerfile: Dockerfile.worker
    image: ${WORKER_IMAGE}
    container_name: worker
    volumes:
      - ${V_WORKER_LOGS}:/usr/app/logs
      - ${V_UPLOADS}:/usr/app/upload
    restart: unless-stopped
    depends_on:
      - redis
      - postgres
    env_file:
      - .env
    networks:
      - backend_network

networks:
  frontend_network:
    driver: bridge
  backend_network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  postgres_logs:
    driver: local
  redis_data:
    driver: local
