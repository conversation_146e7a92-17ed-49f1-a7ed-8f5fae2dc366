#!/bin/sh
# Database Restoration Script
# This script restores a database from a SQL dump file to a fresh TimescaleDB instance

# Get parameters
database="$1"
sql_dump="$2"

# How to use:
# run the command below in your terminal
# ./db_restore.sh <database_name> <sql_dump_file>

# Check if database and sql_dump variables are provided
if [ -z "$database" ] || [ -z "$sql_dump" ]; then
    echo "Usage: $0 <database_name> <sql_dump_file>"
    echo ""
    echo "Example:"
    echo "  ./db_restore.sh prs backup.sql"
    echo "  ./db_restore.sh richard_pre_prod production_backup.sql"
    exit 1
fi

# Validate if sql_dump file exists
if [ ! -f "$sql_dump" ]; then
    echo "Error: SQL dump file '$sql_dump' not found!"
    echo "Please ensure the backup file exists in the current directory"
    exit 1
fi

echo "=== Database Restoration Process Started ==="
echo "Database: $database"
echo "SQL Dump: $sql_dump"
echo ""

# Check if the 'postgres' service is running and then shut it down
echo "Step 1: Stopping existing PostgreSQL service..."
if docker compose ps | grep -q 'postgres'; then
    echo "Stopping and removing the 'postgres' service..."
    docker compose down postgres
else
    echo "The 'postgres' service is not running."
fi

# Remove database directories to ensure fresh start
echo ""
echo "Step 2: Cleaning up old database data..."
# Check for different possible data directory names based on your setup
if [ -d "./cityland_db" ]; then
    echo "Removing ./cityland_db directory..."
    rm -rf ./cityland_db
fi

# Also check for the volume path from .env file
if [ -d "../db/postgresql_data" ]; then
    echo "Removing ../db/postgresql_data directory..."
    rm -rf ../db/postgresql_data
fi

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p ../db/postgresql_data
mkdir -p ../db/postgres_logs

# Start the postgres container with TimescaleDB
echo ""
echo "Step 3: Starting fresh TimescaleDB PostgreSQL container..."
docker-compose up -d postgres

# Wait for the postgres container to be fully up and ready
echo ""
echo "Step 4: Waiting for TimescaleDB PostgreSQL container to be ready..."
echo "This may take a few moments..."

# Wait for PostgreSQL to be ready
until docker exec postgres pg_isready -U admin; do
  echo "Waiting for database to be ready..."
  sleep 2
done
echo "PostgreSQL container is ready."

# Additional wait to ensure TimescaleDB is fully initialized
echo "Waiting for TimescaleDB to fully initialize..."
sleep 15

# Drop the database if it exists (for fresh restore)
echo ""
echo "Step 5: Preparing database for restoration..."
echo "Dropping existing database '$database' if it exists..."
docker exec postgres psql -U admin -d postgres -c "DROP DATABASE IF EXISTS $database;"

# Create the database
echo "Creating fresh database '$database'..."
docker exec postgres psql -U admin -d postgres -c "CREATE DATABASE $database;"

# Enable TimescaleDB extension
echo ""
echo "Step 6: Enabling TimescaleDB extension..."
docker exec postgres psql -U admin -d "$database" -c "CREATE EXTENSION IF NOT EXISTS timescaledb;"

# Create prs_user role if it doesn't exist
echo ""
echo "Step 7: Setting up database roles..."
docker exec postgres psql -U admin -d postgres -c "DO \$\$ BEGIN IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'prs_user') THEN CREATE ROLE prs_user; END IF; END \$\$;"

# Grant necessary permissions to prs_user
echo "Granting permissions to prs_user..."
docker exec postgres psql -U admin -d "$database" -c "GRANT ALL PRIVILEGES ON DATABASE $database TO prs_user;"
docker exec postgres psql -U admin -d "$database" -c "GRANT ALL ON SCHEMA public TO prs_user;"

# Additional wait before importing
echo ""
echo "Step 8: Final preparation before import..."
sleep 5

# Restore the SQL dump
echo ""
echo "Step 9: Restoring database from SQL dump..."
echo "This may take several minutes depending on the size of your dump file..."
echo "Importing data from '$sql_dump'..."

# Import with error handling
if docker exec -i postgres psql -U admin -d "$database" < "$sql_dump"; then
    echo "Database import completed successfully!"
else
    echo "Error: Database import failed!"
    echo "Please check the SQL dump file and try again."
    exit 1
fi

# Verify TimescaleDB hypertables after import
echo ""
echo "Step 10: Verifying TimescaleDB setup..."
echo "Checking hypertables..."
docker exec postgres psql -U admin -d "$database" -c "SELECT hypertable_name, num_chunks FROM timescaledb_information.hypertables LIMIT 5;" || echo "No hypertables found (this is normal if your dump doesn't contain TimescaleDB hypertables)"

# Show database info
echo ""
echo "Step 11: Database restoration summary..."
echo "Database '$database' has been successfully restored!"
echo ""
echo "Database connection details:"
echo "  Host: localhost"
echo "  Port: 5432"
echo "  Database: $database"
echo "  Username: admin"
echo "  Password: admin"
echo ""

# Show table count
echo "Tables in restored database:"
docker exec postgres psql -U admin -d "$database" -c "SELECT schemaname, tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;" | head -20

echo ""
echo "=== Database Restoration Process Completed Successfully ==="
echo ""
echo "You can now start your application services with:"
echo "  docker compose up -d"
echo ""
echo "To connect to the database directly:"
echo "  docker exec -it postgres psql -U admin -d $database"
